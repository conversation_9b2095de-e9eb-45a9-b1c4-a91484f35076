<?php
/**
 * Flush Rewrite Rules Script
 * Run this once after updating URL structure
 */

// Include WordPress
require_once('wp-config.php');
require_once('wp-load.php');

// Force flush rewrite rules
flush_rewrite_rules(true);

echo "✅ Rewrite rules flushed successfully!\n";
echo "🔗 New URL structure is now active:\n";
echo "   - States: /cities/state-name/\n";
echo "   - Cities: /cities/state-name/city-name/\n";
echo "   - Zoos: /zoos/zoo-name/\n";
echo "   - Animals: /animals/animal-name/\n";
echo "\n";
echo "🧪 Test URLs:\n";
echo "   - Visit your homepage and click on state/city links\n";
echo "   - Check bottom bar links\n";
echo "   - Test zoo finder functionality\n";
echo "\n";
echo "⚠️  Remember to update any hardcoded links in your content!\n";
?>
