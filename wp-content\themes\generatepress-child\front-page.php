<?php
/**
 * Homepage Template for Petting Zoo Directory
 */

get_header(); ?>

<div id="primary" class="content-area full-width-content">
    <main id="main" class="site-main">
        
        <!-- Enhanced Hero Section - Trust & Family Focus -->
        <section class="hero-section">
            <div class="hero-background">
                <img src="<?php echo wp_upload_dir()['baseurl']; ?>/2025/06/placeholder.jpg" alt="Family enjoying petting zoo" class="hero-image">
                <div class="hero-overlay"></div>
            </div>
            <div class="hero-content">
                <div class="container">
                    <h1>Discover the Best Petting Zoos Near You</h1>
                    <p>Find amazing petting zoos, animal farms, and interactive experiences perfect for families. Create lasting memories with hands-on animal encounters that kids and dads love.</p>

                    <!-- Simplified Zoo Finder Tool -->
                    <form id="zoo-finder-form" class="zoo-finder-form">
                        <select id="finder-location" name="location" aria-label="Select your city">
                            <option value="">Select Your City</option>
                            <?php
                            $locations = get_terms(array(
                                'taxonomy' => 'location',
                                'hide_empty' => false,
                                'parent' => 0
                            ));

                            foreach ($locations as $state) {
                                $cities = get_terms(array(
                                    'taxonomy' => 'location',
                                    'hide_empty' => false,
                                    'parent' => $state->term_id
                                ));

                                if (!empty($cities)) {
                                    echo '<optgroup label="' . esc_attr($state->name) . '">';
                                    foreach ($cities as $city) {
                                        echo '<option value="' . esc_attr($city->slug) . '">' . esc_html($city->name) . '</option>';
                                    }
                                    echo '</optgroup>';
                                }
                            }
                            ?>
                        </select>
                        <button type="submit" class="btn">Find Petting Zoos</button>
                    </form>
                </div>
            </div>
        </section>

        <!-- Popular Cities Section -->
        <section class="section popular-cities-section">
            <div class="container">
                <h2 class="section-title">Find the Best Petting Zoos by City in the United States</h2>
                <p class="section-subtitle">Looking for a fun, family-friendly petting zoo near you? Browse our curated list of the best-rated petting zoos in major U.S. cities. From weekend outings to birthday adventures, discover top locations where kids and parents love to interact with animals.

If your city isn’t listed yet, don’t worry — we’re adding more every week! You can also use our Petting Zoo Finder tool to locate great experiences nearby.</p>

                <div class="state-cards-grid">
                    <?php
                    // Read and parse the CSV file - Fixed path
                    $upload_dir = wp_upload_dir();
                    $csv_file = $upload_dir['basedir'] . '/2025/06/city-state.csv';
                    $states_data = array();

                    if (file_exists($csv_file)) {
                        $handle = fopen($csv_file, 'r');
                        if ($handle !== FALSE) {
                            $header = fgetcsv($handle); // Skip header row

                            while (($data = fgetcsv($handle)) !== FALSE) {
                                if (!empty($data[0]) && !empty($data[2])) {
                                    $city = trim($data[0]);
                                    $state = trim($data[2]);

                                    if (!isset($states_data[$state])) {
                                        $states_data[$state] = array();
                                    }
                                    $states_data[$state][] = $city;
                                }
                            }
                            fclose($handle);
                        }
                    } else {
                        // Debug: Show file path if CSV not found
                        echo '<!-- CSV file not found at: ' . esc_html($csv_file) . ' -->';
                        echo '<!-- Upload dir: ' . esc_html($upload_dir['basedir']) . ' -->';
                        echo '<!-- File exists check: ' . (file_exists($csv_file) ? 'true' : 'false') . ' -->';
                    }

                    // Sort states alphabetically and limit cities per state
                    ksort($states_data);
                    $displayed_states = 0;
                    $max_states = 60; // Display 12 states in 3 columns, 4 rows

                    if (!empty($states_data)) {
                        foreach ($states_data as $state => $cities) {
                            if ($displayed_states >= $max_states) break;

                            // Limit to 6 cities per state for better display
                            $cities = array_slice($cities, 0, 12);
                            ?>
                            <div class="state-card">
                                <?php
                                // Get state term for proper linking with fallback
                                $state_term = get_term_by('name', $state, 'location');
                                if (!$state_term) {
                                    // Try to find by slug
                                    $state_term = get_term_by('slug', sanitize_title($state), 'location');
                                }

                                if ($state_term) {
                                    $state_link = get_term_link($state_term);
                                } else {
                                    // Create fallback URL using new structure
                                    $state_link = home_url('/cities/' . sanitize_title($state) . '/');
                                }
                                ?>
                                <h3 class="state-title">
                                    <a href="<?php echo esc_url($state_link); ?>">Petting Zoos in <?php echo esc_html($state); ?></a>
                                </h3>
                                <div class="cities-list">
                                    <?php foreach ($cities as $city) : ?>
                                        <?php
                                        // Get city term for proper linking with fallback
                                        $city_term = get_term_by('name', $city, 'location');
                                        if (!$city_term) {
                                            // Try to find by slug
                                            $city_term = get_term_by('slug', sanitize_title($city), 'location');
                                        }

                                        if ($city_term) {
                                            $city_link = get_term_link($city_term);
                                        } else {
                                            // Create fallback URL using new structure
                                            $city_link = home_url('/cities/' . sanitize_title($state) . '/' . sanitize_title($city) . '/');
                                        }
                                        ?>
                                        <a href="<?php echo esc_url($city_link); ?>" class="city-link">
                                            <?php echo esc_html($city); ?>
                                        </a>
                                    <?php endforeach; ?>
                                </div>
                            </div>
                            <?php
                            $displayed_states++;
                        }
                    } else {
                        // Fallback content if no data is available
                        echo '<div class="no-data-message">';
                        echo '<p>Popular cities and states will be displayed here once data is loaded.</p>';
                        echo '</div>';
                    }
                    ?>
                </div>
            </div>
        </section>

        <!-- Explore by Animal Section -->
        <section class="section" style="background: #f8f9fa;">
            <div class="container">
                <h2 class="section-title">Petting Zoos by Animal Type – Find Zoos with Goats, Bunnies, Ponies & More</h2>
                <p class="section-subtitle">Looking for a petting zoo with specific animals your kids love? Explore petting zoos across the U.S. based on the animals they feature — from gentle goats and fluffy bunnies to ponies, llamas, reptiles, and more.

Choose an animal below to discover petting zoos that offer safe, hands-on encounters your family won’t forget.</p>
                
                <div class="animals-grid">
                    <?php
                    $featured_animals = array(
                                'monkeys' => '🐒', 'gorillas' => '🦍', 'orangutans' => '🦍',
                                'dogs' => '🐶', 'wolves' => '🐺',
                                'foxes' => '🦊', 'raccoons' => '🦝',
                                'cats' => '🐱', 
                                'lions' => '🦁', 'tigers' => '🐯', 'leopards' => '🐆',
                                'horses' => '🐴', 'racehorses' => '🐎', 
                                'zebras' => '🦓', 'deers' => '🪼', 'bisons' => '🐃',
                                'cows' => '🐮', 'oxen' => '🐂', 'buffalos' => '🐃',
                                'pigs' => '🐷', 'boars' => '🐗', 'rams' => '🐏', 'sheeps' => '🐑', 'goats' => '🐐',
                                'camels' => '🐫', 'llamas' => '🦙', 'giraffes' => '🦒',
                                'elephants' => '🐘', 'rhinos' => '🦏', 'hippos' => '🦛',
                                'mice' => '🐭', 'rats' => '🐀', 'hamsters' => '🐹',
                                'rabbits' => '🐰', 'chipmunks' => '🐿️', 'hedgehogs' => '🦔',
                                'bats' => '🦇', 'bears' => '🐻', 'polar bears' => '🐻‍❄️',
                                'koalas' => '🐨', 'pandas' => '🐼', 'sloths' => '🦥', 'otters' => '🦦',
                                'skunks' => '🦨', 'kangaroos' => '🦘', 
                                'turkeys' => '🦃', 'chickens' => '🐔', 'roosters' => '🐓',
                                'chicks' => '🐣', 
                                'birds' => '🐦', 'penguins' => '🐧', 'doves' => '🕊️',
                                'eagles' => '🦅', 'ducks' => '🦆', 'geese' => '🦢', 'swans' => '🦢',
                                'owls' => '🦉',  'flamingos' => '🦚',
                                'peacocks' => '🦚', 'parrots' => '🦜',
                                'frogs' => '🐸', 'crocodiles' => '🐊', 'turtles' => '🐢', 'reptiles' => '🐊',
                                'lizards' => '🐊', 'snakes' => '🐍', 
                                'whales' => '🐳', 'dolphins' => '🐬', 'fish' => '🐟',
                                'tropical fish' => '🐠', 'blowfish' => '🐡', 'sharks' => '🦈', 'octopuses' => '🐙'
                    );
                    
                    $animal_terms = get_terms(array(
                        'taxonomy' => 'animal_type',
                        'hide_empty' => true,
                        'number' => 12
                    ));
                    
                    foreach ($animal_terms as $animal) {
                        $animal_url = get_term_link($animal);
                        $icon = isset($featured_animals[$animal->slug]) ? $featured_animals[$animal->slug] : '🐾';
                        ?>
                        <a href="<?php echo esc_url($animal_url); ?>" class="animal-card">
                            <div class="animal-icon"><?php echo $icon; ?></div>
                            <h4><?php echo esc_html($animal->name); ?></h4>
                            <div class="zoo-count"><?php echo $animal->count; ?> Location<?php echo $animal->count !== 1 ? 's' : ''; ?></div>
                        </a>
                        <?php
                    }
                    ?>
                </div>
            </div>
        </section>

        <!-- Plan Your Visit Section -->
        <section class="section">
            <div class="container">
                <h2 class="section-title">Plan the Ultimate Petting Zoo Experience for Your Family</h2>
                <p class="section-subtitle">Get the most out of your visit by choosing petting zoos that offer exactly what your family needs — from fun animal feeding experiences and birthday party packages to accessible amenities and hands-on learning. Whether you're planning a weekend outing or a special event, our petting zoo features guide helps you find the perfect fit.</p>
                
                <div class="zoo-grid">
                    <div class="zoo-card">
                        <div class="zoo-card-content">
                            <h3>🎯 Choose the Right Petting Zoo for Your Family</h3>
                            <p>Search by zoo type, environment, and activity level. Whether you want a traditional farm-style petting zoo or a hands-on exotic animal park, we’ll help you find one that fits your family’s comfort and interests.</p>
                        </div>
                    </div>
                    
                    <div class="zoo-card">
                        <div class="zoo-card-content">
                            <h3>🎉 Petting Zoos with Birthday Parties & Special Events</h3>
                            <p>Many petting zoos offer special packages for birthdays, school field trips, and seasonal celebrations. Browse zoos that make party planning simple and fun for kids of all ages.</p>
                        </div>
                    </div>
                    
                    <div class="zoo-card">
                        <div class="zoo-card-content">
                            <h3>🍎 Petting Zoos with Animal Feeding Experiences</h3>
                            <p>Find zoos that let kids feed goats, llamas, rabbits, and more. Some provide feed on-site; others let you bring safe, approved snacks for an interactive animal encounter.</p>
                        </div>
                    </div>
                    
                    <div class="zoo-card">
                        <div class="zoo-card-content">
                            <h3>🚗 Family-Friendly Amenities</h3>
                            <p>Choose locations with picnic tables, shaded areas, clean restrooms, gift shops, and free or on-site parking for a stress-free family outing.</p>
                        </div>
                    </div>
                                        <div class="zoo-card">
                        <div class="zoo-card-content">
                            <h3>🍎 Petting Zoos with Playgrounds or Activity Zones</h3>
                            <p>Extend your visit with zoos that offer kid-friendly playgrounds, obstacle courses, or outdoor games to burn off energy before heading home.</p>
                        </div>
                    </div>
                                        <div class="zoo-card">
                        <div class="zoo-card-content">
                            <h3>🧼 Clean & Safe Petting Zoos</h3>
                            <p>Search for petting zoos with high hygiene ratings, handwashing stations, and staff-trained safety protocols — perfect for toddlers and young children.</p>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- SEO-Enhanced Why Families Love Petting Zoos Section -->
<section class="section" style="background: linear-gradient(135deg, var(--soft-beige) 0%, var(--soft-beige-dark) 100%);">
    <div class="container">
        <h2 class="section-title">Why Families Love Visiting Petting Zoos</h2>
        <p class="section-subtitle">Petting zoos offer a perfect blend of education, adventure, and quality time for families — from toddlers to teens — with interactive animal experiences, outdoor exploration, and bonding opportunities you’ll remember for years.</p>

        <div class="zoo-grid">
            <div class="zoo-card">
                <div class="zoo-card-content">
                    <h3>👨‍👩‍👧‍👦 Quality Family Bonding Time</h3>
                    <p>Enjoy meaningful time together as a family while kids learn and interact with gentle animals. These moments become cherished memories for both parents and children.</p>
                    <div class="features">
                        <span class="feature-tag">No Devices</span>
                        <span class="feature-tag">Shared Smiles</span>
                    </div>
                </div>
            </div>

            <div class="zoo-card">
                <div class="zoo-card-content">
                    <h3>🎯 Easy Weekend Outings</h3>
                    <p>Petting zoos are low-stress, local adventures that don’t require heavy planning. Perfect for spontaneous weekend family fun or organized group visits.</p>
                    <div class="features">
                        <span class="feature-tag">No Reservations Needed</span>
                        <span class="feature-tag">Half-Day Activity</span>
                    </div>
                </div>
            </div>

            <div class="zoo-card">
                <div class="zoo-card-content">
                    <h3>📵 Educational, Screen-Free Experiences</h3>
                    <p>Step away from screens and dive into the real world of animals. These visits teach responsibility, empathy, and curiosity through hands-on engagement.</p>
                    <div class="features">
                        <span class="feature-tag">STEM Learning</span>
                        <span class="feature-tag">Nature Connection</span>
                    </div>
                </div>
            </div>

            <div class="zoo-card">
                <div class="zoo-card-content">
                    <h3>💰 Affordable Family Fun</h3>
                    <p>Enjoy budget-friendly outings with flexible pricing and family discounts. Many zoos offer free parking and allow picnics to stretch your dollar further.</p>
                    <div class="features">
                        <span class="feature-tag">Family Discounts</span>
                        <span class="feature-tag">Picnic Friendly</span>
                    </div>
                </div>
            </div>

            <!-- ✅ New Card #1 -->
            <div class="zoo-card">
                <div class="zoo-card-content">
                    <h3>🧠 Encourages Curiosity & Learning</h3>
                    <p>Petting zoos are more than fun—they’re full of teachable moments. Kids ask questions, observe animals up close, and explore new interests in a safe space.</p>
                    <div class="features">
                        <span class="feature-tag">Teachable Moments</span>
                        <span class="feature-tag">Curious Minds</span>
                    </div>
                </div>
            </div>

            <!-- ✅ New Card #2 -->
            <div class="zoo-card">
                <div class="zoo-card-content">
                    <h3>🪑 Accessible for All Family Members</h3>
                    <p>Many petting zoos are stroller- and wheelchair-friendly, with shaded areas, restrooms, and benches — making it easy to include everyone from toddlers to grandparents.</p>
                    <div class="features">
                        <span class="feature-tag">Stroller-Friendly</span>
                        <span class="feature-tag">Accessible Design</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>



        <!-- FAQ Section -->
        <section class="section faq-section">
            <div class="container">
                <h2 class="section-title">Frequently Asked Questions</h2>
                
                <div class="faq-item">
                    <div class="faq-question">Can I bring my own food to a petting zoo?</div>
                    <div class="faq-answer">
                        <p>Most petting zoos allow you to bring your own food for picnicking, but policies vary. Some have on-site cafes or snack bars. Always check with the specific petting zoo before your visit.</p>
                    </div>
                </div>
                
                <div class="faq-item">
                    <div class="faq-question">What should I wear to a petting zoo?</div>
                    <div class="faq-answer">
                        <p>Wear comfortable, closed-toe shoes and clothes you don't mind getting dirty. Avoid loose jewelry and bring hand sanitizer. Many petting zoos are outdoors, so dress for the weather.</p>
                    </div>
                </div>
                
                <div class="faq-item">
                    <div class="faq-question">Are petting zoos safe for young children?</div>
                    <div class="faq-answer">
                        <p>Yes, reputable petting zoos prioritize safety with gentle animals, proper supervision, and safety guidelines. Always supervise young children and follow the zoo's rules for interacting with animals.</p>
                    </div>
                </div>
                
                <div class="faq-item">
                    <div class="faq-question">Do I need to make reservations?</div>
                    <div class="faq-answer">
                        <p>While many petting zoos accept walk-ins, it's always best to call ahead, especially for larger groups, special events, or during peak seasons like spring and summer.</p>
                    </div>
                </div>
                
                <div class="faq-item">
                    <div class="faq-question">What's the best time to visit a petting zoo?</div>
                    <div class="faq-answer">
                        <p>Animals are typically most active in the morning and late afternoon. Weekdays are usually less crowded than weekends. Spring and fall offer the most comfortable weather for outdoor visits.</p>
                    </div>
                </div>
            </div>
        </section>

    </main>
</div>

<?php get_footer(); ?>
