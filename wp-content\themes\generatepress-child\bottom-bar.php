<div class="bottom-bar">
    <div class="bottom-bar-container">
        <div class="bottom-bar-section">
            <h3>About</h3>
            <ul>
                <li><a href="<?php echo home_url('/about'); ?>">About Petting Zoos</a></li>
                <li><a href="<?php echo home_url('/privacy'); ?>">Privacy Policy</a></li>
                <li><a href="<?php echo home_url('/contact'); ?>">Contact Us</a></li>
            </ul>
        </div>
        <div class="bottom-bar-section">
            <h3>Animals</h3>
            <ul>
                <li><a href="<?php echo home_url('/animals/rhinos/'); ?>">Rhinos</a></li>
                <li><a href="<?php echo home_url('/animals/capybaras/'); ?>">Capybaras</a></li>
                <li><a href="<?php echo home_url('/animals/elephants/'); ?>">Elephants</a></li>
                <li><a href="<?php echo home_url('/animals/dolphins/'); ?>">Dolphins</a></li>
                <li><a href="<?php echo home_url('/animals/goats/'); ?>">Goats</a></li>
                <li><a href="<?php echo home_url('/animals/chickens/'); ?>">Chickens</a></li>
                <li><a href="<?php echo home_url('/animals/gorillas/'); ?>">Gorillas</a></li>
                <li><a href="<?php echo home_url('/animals/pandas/'); ?>">Pandas</a></li>
                <li><a href="<?php echo home_url('/animals/reptiles/'); ?>">Reptiles</a></li>
                <li><a href="<?php echo home_url('/animals/butterflies/'); ?>">Butterflies</a></li>
            </ul>
        </div>
        <div class="bottom-bar-section">
            <h3>Popular States</h3>
            <ul>
                <?php
                // Get popular state terms dynamically for proper linking
                $popular_states = array('Texas', 'Alabama', 'California', 'Florida', 'Nevada');
                foreach ($popular_states as $state_name) {
                    $state_term = get_term_by('name', $state_name, 'location');
                    if (!$state_term) {
                        $state_term = get_term_by('slug', sanitize_title($state_name), 'location');
                    }

                    if ($state_term) {
                        $state_link = get_term_link($state_term);
                    } else {
                        // Fallback for states that don't exist as terms yet
                        $state_link = home_url('/cities/' . sanitize_title($state_name) . '/');
                    }
                    echo '<li><a href="' . esc_url($state_link) . '">' . esc_html($state_name) . '</a></li>';
                }
                ?>
            </ul>
        </div>
        <div class="bottom-bar-section">
            <h3>Popular Cities</h3>
            <ul>
                <?php
                // Get popular city terms dynamically for proper linking with state context
                $popular_cities = array(
                    'Miami' => 'Florida',
                    'Houston' => 'Texas',
                    'Dallas' => 'Texas',
                    'Philadelphia' => 'Pennsylvania',
                    'New York' => 'New York',
                    'Salt Lake City' => 'Utah',
                    'Seattle' => 'Washington',
                    'Detroit' => 'Michigan',
                    'Omaha' => 'Nebraska',
                    'Las Vegas' => 'Nevada',
                    'Boston' => 'Massachusetts'
                );

                foreach ($popular_cities as $city_name => $state_name) {
                    $city_term = get_term_by('name', $city_name, 'location');
                    if (!$city_term) {
                        $city_term = get_term_by('slug', sanitize_title($city_name), 'location');
                    }

                    if ($city_term) {
                        $city_link = get_term_link($city_term);
                    } else {
                        // Fallback with proper state/city structure
                        $city_link = home_url('/cities/' . sanitize_title($state_name) . '/' . sanitize_title($city_name) . '/');
                    }
                    echo '<li><a href="' . esc_url($city_link) . '">' . esc_html($city_name) . '</a></li>';
                }
                ?>
            </ul>
        </div>
    </div>
    <div class="bottom-bar-copyright">
        <p>&copy; <?php echo date("Y"); ?> All rights Reserved.</p>
    </div>
</div>
