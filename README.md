# Petting Zoo Directory Website

A comprehensive WordPress directory website for petting zoos across the United States, built with SEO optimization and user experience in mind.

## 🎯 Project Overview

This project creates a directory website around the keyword "petting zoo" that lists petting zoos throughout the US. The site is built on WordPress using a GeneratePress child theme and includes custom functionality for managing and displaying petting zoo listings.

**Domain**: http://pettingzoos.local/

## 🏗️ Architecture

### Custom Post Type
- **petting_zoo**: Main content type for individual petting zoo listings

### Custom Taxonomies (SEO-Optimized)
1. **location** (Hierarchical: State → City)
   - URL: `/cities/state/city/`
   - Purpose: Geographic organization and local SEO

2. **animal_type** (Non-hierarchical)
   - URL: `/animals/animal-name/`
   - Purpose: Animal-specific searches ("capybara petting", "sloth encounters")

3. **zoo_type** (Non-hierarchical)
   - URL: `/zoo-type/type-name/`
   - Purpose: Type-based searches ("mobile petting zoo", "farm")

4. **features** (Non-hierarchical)
   - URL: `/features/feature-name/`
   - Purpose: Amenity-based searches ("playground", "picnic areas")

5. **event_type** (Non-hierarchical)
   - URL: `/events/event-name/`
   - Purpose: Event-based searches ("birthday parties", "field trips")

## 📁 File Structure

```
wp-content/
├── themes/
│   └── generatepress-child/
│       ├── style.css                    # Child theme styles
│       ├── functions.php                # Theme functionality
│       ├── front-page.php              # Homepage template
│       ├── single-petting_zoo.php      # Individual zoo pages
│       ├── taxonomy-location.php       # City/state pages
│       ├── taxonomy-features.php       # Feature pages
│       └── assets/
│           └── js/
│               └── petting-zoo.js       # Frontend JavaScript
└── plugins/
    └── petting-zoo-importer/
        ├── petting-zoo-importer.php     # Main plugin file
        ├── sample-data.json             # Example import data
        └── assets/
            ├── importer.js              # Admin JavaScript
            └── importer.css             # Admin styles
```

## 🎨 Homepage SEO Structure

The homepage follows the specified SEO wireframe:

### Hero Section
- **H1**: "Find the Best Petting Zoo Near You for a Family-Friendly Adventure"
- **Purpose**: Targets "petting zoo near me" and "petting zoo" queries
- **Features**: Interactive finder tool with location and animal type filters

### Key Sections
1. **Zoo Finder Tool**: Dropdown for cities + geolocation "Near Me" feature
2. **Popular Cities**: Internal links with keyword-rich anchor text
3. **Explore by Animal**: Grid linking to animal-focused pages
4. **Plan Your Visit**: Information about amenities and planning
5. **Why Fathers Love It**: Targeted content for the primary audience
6. **FAQ Section**: Natural language queries for featured snippets

## 🔧 Custom Importer Plugin

### Features
- **File Selection**: Native file explorer (not WordPress media upload)
- **Simulation Mode**: Test imports without making database changes
- **Import Mode**: Actually import the data
- **Detailed Logging**: Comprehensive logs for debugging
- **Data Replacement**: Automatically handles existing zoo updates
- **JSON Processing**: Structured data import from JSON files

### Usage
1. Navigate to **Petting Zoos → Import Data** in WordPress admin
2. Select a JSON file using the file picker
3. Click **Simulate Import** to test the data
4. Review the log for any errors or warnings
5. Click **Import Data** to perform the actual import

### JSON Structure
```json
{
  "petting_zoos": [
    {
      "name": "Zoo Name",
      "description": "Full description",
      "excerpt": "Short excerpt",
      "address": "Full address",
      "phone": "(*************",
      "website": "https://example.com",
      "hours": "Operating hours",
      "admission": "Pricing information",
      "latitude": "39.7817",
      "longitude": "-89.6501",
      "location": ["State", "City"],
      "animal_type": ["Goats", "Sheep"],
      "zoo_type": ["Farm"],
      "features": ["Picnic Areas", "Playground"],
      "event_type": ["Birthday Parties"],
      "featured_image": "https://example.com/image.jpg"
    }
  ]
}
```

## 🎯 SEO Strategy

### URL Structure
- **Homepage**: `/`
- **Individual Zoos**: `/zoos/zoo-name/`
- **City Pages**: `/cities/state/city/`
- **State Pages**: `/cities/state/`
- **Animal Pages**: `/animals/animal-type/`
- **Feature Pages**: `/features/feature-name/`
- **Event Pages**: `/events/event-type/`

### Target Keywords
- Primary: "petting zoo near me", "petting zoo"
- Location-based: "petting zoo in [city]", "petting zoos [state]"
- Animal-specific: "capybara petting", "sloth encounters"
- Feature-based: "petting zoo with playground"
- Event-based: "petting zoo birthday parties"

## 🚀 Features

### Frontend Features
- **Responsive Design**: Mobile-first approach
- **Interactive Finder**: Location and animal type filtering
- **Geolocation**: "Near Me" functionality
- **Search Autocomplete**: Real-time search suggestions
- **FAQ Accordion**: Expandable FAQ sections
- **Smooth Scrolling**: Enhanced user experience
- **Image Lazy Loading**: Performance optimization

### Admin Features
- **Custom Meta Boxes**: Easy data entry for zoo details
- **Taxonomy Management**: Organized content categorization
- **Import System**: Bulk data import capabilities
- **Detailed Logging**: Comprehensive import tracking

### Technical Features
- **AJAX Integration**: Dynamic content loading
- **SEO Optimization**: Structured URLs and meta data
- **Performance**: Optimized CSS and JavaScript
- **Accessibility**: WCAG-compliant design elements

## 🛠️ Installation & Setup

1. **Install WordPress** with the provided local setup
2. **Activate GeneratePress** parent theme
3. **Install Child Theme**: Upload the `generatepress-child` folder
4. **Install Importer Plugin**: Upload the `petting-zoo-importer` folder
5. **Activate Both**: Enable child theme and importer plugin
6. **Import Sample Data**: Use the provided `sample-data.json`

## 📊 Content Strategy

### Page Types Created
1. **Homepage**: SEO-optimized landing page
2. **Individual Zoo Pages**: Detailed listings with all information
3. **City Pages**: Location-based directory pages
4. **Feature Pages**: Amenity-focused landing pages
5. **Taxonomy Archives**: Organized content browsing

### Internal Linking Strategy
- City pages link to individual zoos in that city
- Feature pages link to zoos with those features
- Individual zoo pages link to related zoos
- Homepage links to popular cities and features

## 🔮 Future Enhancements

- **Map Integration**: Google Maps for zoo locations
- **Review System**: User ratings and reviews
- **Advanced Filtering**: Multiple simultaneous filters
- **Mobile App**: Companion mobile application
- **API Integration**: Third-party data sources
- **Social Features**: User-generated content

## 📝 Notes

- All custom code follows WordPress coding standards
- SEO-friendly URLs are automatically generated
- The importer handles data validation and error reporting
- The design is fully responsive and accessible
- Performance optimizations are built-in

This implementation provides a solid foundation for a comprehensive petting zoo directory website with room for future expansion and enhancement.
