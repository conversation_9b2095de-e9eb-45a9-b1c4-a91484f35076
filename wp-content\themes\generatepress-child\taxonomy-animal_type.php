<?php
/**
 * Animal Type Taxonomy Template (Animal Experience Pages)
 */

get_header();

$current_term = get_queried_object();
$animal_name = $current_term->name;
$animal_slug = $current_term->slug;

// Get zoo count for this animal type
$zoo_count = $current_term->count;
?>

<div id="primary" class="content-area full-width-content">
    <main id="main" class="site-main">

        <!-- Enhanced Page Header - Zoo Style -->
        <div class="city-hero-section">
            <div class="hero-overlay"></div>
            <div class="hero-content-wrapper">
                <div class="hero-content">
                    <div class="breadcrumbs text-center">
                        <a href="<?php echo home_url(); ?>">Home</a>
                        <span class="separator">›</span>
                        <a href="<?php echo home_url('/animals/'); ?>">Animal Experiences</a>
                        <span class="separator">›</span>
                        <span class="current"><?php echo esc_html($animal_name); ?></span>
                    </div>

                    <h1 class="city-title"><?php echo esc_html($animal_name); ?> Petting Experiences</h1>
                    
                    <div class="city-subtitle">
                        <p>Find petting zoos where you can interact with <?php echo esc_html(strtolower($animal_name)); ?>s</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Description Section -->
        <div class="container" style="max-width: 1400px; margin: 0 auto; padding: 2rem 2rem 0;">
            <div class="city-intro-content text-center" style="max-width: 800px; margin: 0 auto;">
                <?php if ($current_term->description) : ?>
                    <?php echo wpautop($current_term->description); ?>
                <?php else : ?>
                    <p>Discover amazing <?php echo esc_html(strtolower($animal_name)); ?> petting experiences at family-friendly locations. 
                    We've found <?php echo $zoo_count; ?> petting zoo<?php echo $zoo_count !== 1 ? 's' : ''; ?> 
                    where you can safely interact with these wonderful animals.</p>
                <?php endif; ?>
            </div>

            <!-- H2: Best Places for [Animal] Petting -->
            <section class="animal-zoos-section">
                <h2>Best Places for <?php echo esc_html($animal_name); ?> Petting</h2>
                <p>These carefully selected petting zoos offer safe, supervised interactions with <?php echo esc_html(strtolower($animal_name)); ?>s. 
                Each location maintains high standards for animal welfare and visitor safety.</p>

                <!-- Enhanced Filters Section -->
                <div class="zoo-filters-section">
                    <h3>🔍 Find Your Perfect <?php echo esc_html($animal_name); ?> Experience</h3>
                    <div class="zoo-filters">
                        <select name="location" onchange="applyFilters()" aria-label="Filter by location">
                            <option value="">📍 All Locations</option>
                            <?php
                            // Get states first
                            $states = get_terms(array(
                                'taxonomy' => 'location',
                                'hide_empty' => true,
                                'parent' => 0
                            ));

                            $selected_location = isset($_GET['location']) ? $_GET['location'] : '';

                            foreach ($states as $state) {
                                $selected = ($selected_location === $state->slug) ? 'selected' : '';
                                echo '<option value="' . esc_attr($state->slug) . '" ' . $selected . '>' . esc_html($state->name) . '</option>';
                                
                                // Get cities in this state
                                $cities = get_terms(array(
                                    'taxonomy' => 'location',
                                    'hide_empty' => true,
                                    'parent' => $state->term_id
                                ));
                                
                                foreach ($cities as $city) {
                                    $selected = ($selected_location === $city->slug) ? 'selected' : '';
                                    echo '<option value="' . esc_attr($city->slug) . '" ' . $selected . '>— ' . esc_html($city->name) . ', ' . esc_html($state->name) . '</option>';
                                }
                            }
                            ?>
                        </select>

                        <select name="features" onchange="applyFilters()" aria-label="Filter by features">
                            <option value="">🎯 All Features</option>
                            <?php
                            $features = get_terms(array(
                                'taxonomy' => 'features',
                                'hide_empty' => true
                            ));

                            $selected_feature = isset($_GET['features']) ? $_GET['features'] : '';

                            foreach ($features as $feature) {
                                $selected = ($selected_feature === $feature->slug) ? 'selected' : '';
                                echo '<option value="' . esc_attr($feature->slug) . '" ' . $selected . '>' . esc_html($feature->name) . '</option>';
                            }
                            ?>
                        </select>

                        <select name="zoo_type" onchange="applyFilters()" aria-label="Filter by zoo type">
                            <option value="">🏡 All Zoo Types</option>
                            <?php
                            $zoo_types = get_terms(array(
                                'taxonomy' => 'zoo_type',
                                'hide_empty' => true
                            ));

                            $selected_zoo_type = isset($_GET['zoo_type']) ? $_GET['zoo_type'] : '';

                            foreach ($zoo_types as $zoo_type) {
                                $selected = ($selected_zoo_type === $zoo_type->slug) ? 'selected' : '';
                                echo '<option value="' . esc_attr($zoo_type->slug) . '" ' . $selected . '>' . esc_html($zoo_type->name) . '</option>';
                            }
                            ?>
                        </select>
                    </div>
                </div>

                <script>
                function applyFilters() {
                    const location = document.querySelector('select[name="location"]').value;
                    const features = document.querySelector('select[name="features"]').value;
                    const zoo_type = document.querySelector('select[name="zoo_type"]').value;
                    
                    let url = new URL(window.location);
                    
                    if (location) url.searchParams.set('location', location);
                    else url.searchParams.delete('location');
                    
                    if (features) url.searchParams.set('features', features);
                    else url.searchParams.delete('features');
                    
                    if (zoo_type) url.searchParams.set('zoo_type', zoo_type);
                    else url.searchParams.delete('zoo_type');
                    
                    window.location = url;
                }
                </script>

                <?php
                // Query for petting zoos with this animal type
                $args = array(
                    'post_type' => 'petting_zoo',
                    'posts_per_page' => -1,
                    'tax_query' => array(
                        array(
                            'taxonomy' => 'animal_type',
                            'field' => 'slug',
                            'terms' => $animal_slug,
                        ),
                    ),
                );

                // Add location filter
                if (isset($_GET['location']) && !empty($_GET['location'])) {
                    $args['tax_query'][] = array(
                        'taxonomy' => 'location',
                        'field' => 'slug',
                        'terms' => sanitize_text_field($_GET['location']),
                    );
                }

                // Add features filter
                if (isset($_GET['features']) && !empty($_GET['features'])) {
                    $args['tax_query'][] = array(
                        'taxonomy' => 'features',
                        'field' => 'slug',
                        'terms' => sanitize_text_field($_GET['features']),
                    );
                }

                // Add zoo type filter
                if (isset($_GET['zoo_type']) && !empty($_GET['zoo_type'])) {
                    $args['tax_query'][] = array(
                        'taxonomy' => 'zoo_type',
                        'field' => 'slug',
                        'terms' => sanitize_text_field($_GET['zoo_type']),
                    );
                }

                $zoo_query = new WP_Query($args);
                ?>

                <div class="results-info">
                    <p>Found <?php echo $zoo_query->found_posts; ?> petting zoo<?php echo $zoo_query->found_posts !== 1 ? 's' : ''; ?> 
                    with <?php echo esc_html(strtolower($animal_name)); ?> experiences</p>
                </div>

                <!-- Petting Zoos Grid -->
                <?php if ($zoo_query->have_posts()) : ?>
                    <div class="zoo-grid">
                        <?php while ($zoo_query->have_posts()) : $zoo_query->the_post(); ?>
                            <div class="zoo-card">
                                <?php if (has_post_thumbnail()) : ?>
                                    <a href="<?php the_permalink(); ?>">
                                        <?php the_post_thumbnail('medium'); ?>
                                    </a>
                                <?php endif; ?>
                                
                                <div class="zoo-card-content">
                                    <h3><a href="<?php the_permalink(); ?>"><?php the_title(); ?></a></h3>
                                    
                                    <?php
                                    $address = get_post_meta(get_the_ID(), '_petting_zoo_address', true);
                                    if ($address) : ?>
                                        <p class="zoo-address">📍 <?php echo esc_html($address); ?></p>
                                    <?php endif; ?>
                                    
                                    <div class="zoo-excerpt">
                                        <?php echo wp_trim_words(get_the_excerpt(), 15); ?>
                                    </div>
                                    
                                    <a href="<?php the_permalink(); ?>" class="btn btn-primary">View Details</a>
                                </div>
                            </div>
                        <?php endwhile; ?>
                    </div>
                <?php else : ?>
                    <div class="no-results">
                        <h3>No <?php echo esc_html($animal_name); ?> Experiences Found</h3>
                        <p>We couldn't find any petting zoos with <?php echo esc_html(strtolower($animal_name)); ?> experiences matching your criteria. 
                        Try adjusting your filters or <a href="<?php echo home_url(); ?>">browse all petting zoos</a>.</p>
                    </div>
                <?php endif; ?>

                <?php wp_reset_postdata(); ?>
            </section>

            <!-- H2: What to Expect with [Animal] Petting -->
            <section class="animal-experience-section">
                <h2>What to Expect with <?php echo esc_html($animal_name); ?> Petting</h2>
                <div class="experience-grid">
                    <div class="experience-item">
                        <h4>🤲 Safe Interactions</h4>
                        <p>All <?php echo esc_html(strtolower($animal_name)); ?> interactions are supervised by trained staff to ensure safety for both visitors and animals.</p>
                    </div>
                    <div class="experience-item">
                        <h4>📚 Educational Value</h4>
                        <p>Learn about <?php echo esc_html(strtolower($animal_name)); ?> behavior, diet, and habitat while enjoying hands-on experiences.</p>
                    </div>
                    <div class="experience-item">
                        <h4>👨‍👩‍👧‍👦 Family Fun</h4>
                        <p>Perfect activity for families with children of all ages to create lasting memories together.</p>
                    </div>
                    <div class="experience-item">
                        <h4>📸 Photo Opportunities</h4>
                        <p>Capture amazing photos and videos of your <?php echo esc_html(strtolower($animal_name)); ?> petting experience.</p>
                    </div>
                </div>
            </section>

            <!-- H2: Tips for [Animal] Petting -->
            <section class="animal-tips-section">
                <h2>Tips for <?php echo esc_html($animal_name); ?> Petting</h2>
                <div class="tips-content">
                    <ul>
                        <li><strong>Follow Staff Instructions:</strong> Always listen to the guidance provided by zoo staff for safe interactions.</li>
                        <li><strong>Gentle Touch:</strong> Use slow, gentle movements when petting <?php echo esc_html(strtolower($animal_name)); ?>s.</li>
                        <li><strong>Hand Hygiene:</strong> Wash your hands before and after animal interactions.</li>
                        <li><strong>Respect Boundaries:</strong> If an animal seems stressed or moves away, give it space.</li>
                        <li><strong>Bring Hand Sanitizer:</strong> Many facilities provide it, but it's good to have your own.</li>
                        <li><strong>Wear Appropriate Clothing:</strong> Closed-toe shoes and clothes you don't mind getting a bit dirty.</li>
                    </ul>
                </div>
            </section>

        </div>
    </main>
</div>

<?php
get_footer();
?>
